---
type: "always_apply"
description: "Package development guidelines for figma-restoration-mcp-vue-tools in Cursor IDE"
---

# Package Development Guidelines for Cursor IDE

## npm Package Context
This is the `figma-restoration-mcp-vue-tools` package - a professional toolkit for Figma component restoration with MCP integration, optimized for Cursor IDE development.

## Cursor IDE Specific Guidelines

### AI Assistant Integration
- Leverage Cursor's AI capabilities for code generation and refactoring
- Use AI-assisted debugging for complex MCP tool issues
- Utilize AI for documentation generation and improvement
- Apply AI suggestions for code optimization and best practices

### Code Completion and IntelliSense
- Configure proper TypeScript definitions for better autocomplete
- Use JSDoc comments extensively for AI context
- Maintain clear variable and function naming for AI understanding
- Structure code with clear patterns for AI assistance

### Development Workflow
- Use Cursor's integrated terminal for npm commands
- Leverage AI chat for troubleshooting and problem-solving
- Utilize code suggestions for Vue component development
- Apply AI-assisted refactoring for code improvements

## MCP Tool Development in Cursor

### Tool Creation Process
1. Use AI to generate initial tool structure
2. Implement MCP protocol compliance with AI assistance
3. Test tools using Cursor's debugging capabilities
4. Optimize performance with AI suggestions

### Vue Component Development
- Use Cursor's Vue 3 snippets and templates
- Leverage AI for component composition and structure
- Apply AI suggestions for styling and layout
- Utilize AI for accessibility improvements

### Testing and Debugging
- Use Cursor's integrated debugging for MCP tools
- Leverage AI for test case generation
- Apply AI assistance for error diagnosis
- Utilize AI for performance optimization

## File Organization for Cursor

### Workspace Configuration
```json
{
  "folders": [
    {
      "name": "MCP Tools",
      "path": "./src/tools"
    },
    {
      "name": "Vue Components", 
      "path": "./src/components"
    },
    {
      "name": "Documentation",
      "path": "./docs"
    },
    {
      "name": "Configuration",
      "path": "./config"
    }
  ]
}
```

### Recommended Extensions
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag
- Bracket Pair Colorizer

## AI-Assisted Development Patterns

### Code Generation
- Use AI to generate MCP tool boilerplate
- Generate Vue component templates with AI
- Create test cases using AI assistance
- Generate documentation with AI help

### Code Review and Optimization
- Use AI for code review and suggestions
- Apply AI recommendations for performance improvements
- Leverage AI for security vulnerability detection
- Utilize AI for code style consistency

### Problem Solving
- Use AI chat for debugging complex issues
- Get AI assistance for MCP protocol questions
- Leverage AI for Vue 3 best practices
- Apply AI suggestions for architecture decisions

## Cursor-Specific Features

### Multi-file Editing
- Use Cursor's multi-file editing for consistent changes
- Apply bulk refactoring across MCP tools
- Maintain consistency in Vue component structure
- Update documentation across multiple files

### AI Code Suggestions
- Accept AI suggestions for boilerplate code
- Use AI for complex algorithm implementation
- Leverage AI for error handling patterns
- Apply AI suggestions for code organization

### Integrated Development
- Use Cursor's integrated terminal for package commands
- Leverage built-in Git integration for version control
- Utilize AI-powered search and replace
- Apply AI assistance for project navigation

## Best Practices for Cursor Development

### Project Setup
1. Configure Cursor workspace settings
2. Set up AI assistant preferences
3. Install recommended extensions
4. Configure debugging and testing

### Daily Development
- Start with AI-assisted planning
- Use AI for code generation and completion
- Leverage AI for debugging and troubleshooting
- Apply AI suggestions for code improvement

### Code Quality
- Use AI for code review before commits
- Apply AI suggestions for refactoring
- Leverage AI for test coverage improvement
- Utilize AI for documentation updates

### Collaboration
- Use AI to generate clear commit messages
- Leverage AI for pull request descriptions
- Apply AI assistance for code documentation
- Utilize AI for issue reporting and resolution
