---
type: "always_apply"
---

# 🔄 Figma Self-Reflective工作流程实施指南

## 🎯 触发机制

### 自动触发条件
```javascript
// 在figma_compare工具执行后检查
if (comparisonResult.matchPercentage < 89) {
  console.log("🚨 还原度不达标，启动Self-Reflective分析");
  await executeSelfReflectiveWorkflow();
}
```

## 📋 Self-Reflective执行清单

### Phase 1: 重新获取和分析Figma数据
```markdown
🔍 **步骤1.1: 重新获取Figma JSON**
- [ ] 使用get_figma_data_figma-local重新获取原始数据
- [ ] 确保数据完整性和最新性
- [ ] 保存为figma-data-reanalysis.json

🔍 **步骤1.2: 深度重新分析JSON结构**
- [ ] 逐个分析每个节点的type、boundingBox、fills
- [ ] 重新构建完整的元素层级关系
- [ ] 识别之前可能遗漏的嵌套结构

🔍 **步骤1.3: 素材识别重新验证**
- [ ] 扫描所有fills字段，查找imageRef
- [ ] 重新分类：位图 vs SVG vs CSS可实现
- [ ] 生成完整的素材清单和下载列表
```

### Phase 2: 素材/Icon分析验证
```markdown
🎨 **步骤2.1: 素材完整性检查**
- [ ] 对比当前下载的素材与JSON中的imageRef
- [ ] 识别缺失的素材文件
- [ ] 验证素材文件路径和命名

🎨 **步骤2.2: 素材用途重新分析**
- [ ] 背景图片 vs 装饰图标 vs 内容图片
- [ ] SVG图标 vs 复杂图形
- [ ] 可CSS实现 vs 必须使用图片

🎨 **步骤2.3: 素材处理**
- [ ] 手动准备所需的图片素材
- [ ] 验证图片质量和尺寸
- [ ] 更新组件中的图片引用路径
```

### Phase 3: 元素位置精确性验证
```markdown
📐 **步骤3.1: 坐标系统重新计算**
- [ ] Figma坐标 → CSS坐标转换验证
- [ ] 父子元素相对位置关系检查
- [ ] z-index层级关系验证

📐 **步骤3.2: 布局模式重新分析**
- [ ] Flex布局 vs 绝对定位选择
- [ ] 容器尺寸和约束条件
- [ ] 响应式适配影响

📐 **步骤3.3: 精确位置计算**
- [ ] 重新计算每个元素的精确坐标
- [ ] 验证margin、padding、border的影响
- [ ] 检查文本基线和对齐方式
```

### Phase 4: 差异区域智能诊断
```markdown
🔍 **步骤4.1: 区域差异映射**
- [ ] 将差异区域坐标映射到Figma元素
- [ ] 识别每个差异区域对应的具体元素
- [ ] 分析差异的根本原因

🔍 **步骤4.2: 问题类型分类**
- [ ] 大面积差异：素材问题、布局错误
- [ ] 中等差异：位置偏移、尺寸不匹配  
- [ ] 小面积差异：样式细节、字体渲染

🔍 **步骤4.3: 优先级排序**
- [ ] 按影响程度排序问题列表
- [ ] 制定修复优先级策略
- [ ] 估算修复难度和效果
```

### Phase 5: 针对性修复实施
```markdown
🔧 **步骤5.1: 素材问题修复**
- [ ] 重新下载缺失或错误的素材
- [ ] 更新Vue组件中的图片引用
- [ ] 验证图片显示效果

🔧 **步骤5.2: 位置问题修复**
- [ ] 重新计算和设置元素坐标
- [ ] 调整CSS定位方式和参数
- [ ] 优化布局结构和约束

🔧 **步骤5.3: 样式问题修复**
- [ ] 精确匹配颜色、字体、边框
- [ ] 调整间距、对齐、尺寸
- [ ] 优化视觉效果和细节
```

### Phase 6: 迭代验证和记录
```markdown
📊 **步骤6.1: 修复效果验证**
- [ ] 重新运行figma_compare工具
- [ ] 计算还原度改进幅度
- [ ] 分析剩余差异区域

📊 **步骤6.2: 迭代决策**
- [ ] 如果还原度 ≥ 98%：完成
- [ ] 如果还原度 < 98% 且迭代次数 < 3：继续迭代
- [ ] 如果迭代次数 ≥ 3：输出详细分析报告

📊 **步骤6.3: 过程记录**
- [ ] 记录发现的问题和修复方案
- [ ] 记录每次迭代的改进效果
- [ ] 生成Self-Reflective分析报告
```

## 📝 Self-Reflective分析报告模板

```markdown
# 🧠 Self-Reflective分析报告

## 基本信息
- 组件名称: {componentName}
- 初始还原度: {initialMatchPercentage}%
- 目标还原度: 98%
- 迭代次数: {iterationCount}

## 重新分析发现的问题

### 1. Figma JSON分析问题
- [ ] 遗漏的节点: [列表]
- [ ] 错误的层级关系: [详情]
- [ ] 坐标计算错误: [详情]

### 2. 素材/Icon识别问题  
- [ ] 缺失的素材: [列表]
- [ ] 错误分类的元素: [列表]
- [ ] 下载失败的资源: [列表]

### 3. 元素位置问题
- [ ] 位置偏移的元素: [列表]
- [ ] 布局方式错误: [详情]
- [ ] 尺寸计算错误: [详情]

## 修复方案和效果

### 迭代1
- 修复内容: [详情]
- 还原度变化: {before}% → {after}%
- 主要改进: [描述]

### 迭代2 (如有)
- 修复内容: [详情]  
- 还原度变化: {before}% → {after}%
- 主要改进: [描述]

### 迭代3 (如有)
- 修复内容: [详情]
- 还原度变化: {before}% → {after}%
- 主要改进: [描述]

## 最终结果
- 最终还原度: {finalMatchPercentage}%
- 是否达标: {达标/未达标}
- 剩余主要问题: [列表]

## 经验总结
- 主要问题类型: [总结]
- 有效修复策略: [总结]
- 改进建议: [建议]
```

## ⚡ 实施要点

### 关键成功因素
1. **系统性重新分析**: 不能只看表面差异，要深入重新分析JSON
2. **问题根因定位**: 准确识别差异的根本原因
3. **针对性修复**: 根据问题类型选择最有效的修复策略
4. **迭代控制**: 避免无限循环，最多3次迭代
5. **过程记录**: 详细记录分析和修复过程

### 常见陷阱避免
- ❌ 只修改表面样式，不解决根本问题
- ❌ 忽略素材完整性检查
- ❌ 位置计算不够精确
- ❌ 没有系统性重新分析JSON
- ❌ 迭代没有明确目标和改进策略

### 质量保证
- ✅ 每次迭代都有明确的改进目标
- ✅ 修复方案基于深度分析结果
- ✅ 验证修复效果并记录改进
- ✅ 达到98%还原度或完成3次迭代后结束
